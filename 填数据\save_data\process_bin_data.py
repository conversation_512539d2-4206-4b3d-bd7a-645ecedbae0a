def process_bin_data(input_file, output_file):
    """
    处理二进制数据文件，将每行的数据重新排列
    
    功能：
    - 读取每行128位的二进制数据
    - 将每行拆分为前64位（高位）和后64位（低位）
    - 重新排列：后64位（低位）在前一行，前64位（高位）在后一行
    
    参数:
    input_file: 输入文件路径
    output_file: 输出文件路径
    """
    
    with open(input_file, 'r', encoding='utf-8') as infile, open(output_file, 'w', encoding='utf-8') as outfile:
        lines = infile.readlines()
        
        for line in lines:
            # 移除换行符和空白字符
            line = line.strip()
            
            # 检查行长度是否为128位
            if len(line) != 128:
                print(f"警告：行长度不是128位，实际长度为{len(line)}位: {line[:50]}...")
                continue
            
            # 拆分为前64位（高位）和后64位（低位）
            high_64_bits = line[:64]   # 前64位（高位）
            low_64_bits = line[64:]    # 后64位（低位）
            
            # 按要求排列：后64位（低位）在前，前64位（高位）在后
            outfile.write(low_64_bits + '\n')   # 先写入低位
            outfile.write(high_64_bits + '\n')  # 再写入高位
    
    print(f"处理完成！")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")


def process_bin_data_with_spacing(input_file, output_file):
    """
    处理二进制数据文件，将每行的数据重新排列，并保持16位分组的空格格式
    
    功能：
    - 读取每行的二进制数据（可能包含空格分隔的16位数据）
    - 将每行拆分为前64位（高位）和后64位（低位）
    - 重新排列并保持16位分组格式
    
    参数:
    input_file: 输入文件路径
    output_file: 输出文件路径
    """
    
    with open(input_file, 'r', encoding='utf-8') as infile, open(output_file, 'w', encoding='utf-8') as outfile:
        lines = infile.readlines()
        
        for line in lines:
            # 移除换行符
            line = line.strip()
            
            # 移除所有空格，获得连续的二进制字符串
            binary_data = line.replace(' ', '')
            
            # 检查数据长度
            if len(binary_data) != 128:
                print(f"警告：行长度不是128位，实际长度为{len(binary_data)}位")
                continue
            
            # 拆分为前64位（高位）和后64位（低位）
            high_64_bits = binary_data[:64]   # 前64位（高位）
            low_64_bits = binary_data[64:]    # 后64位（低位）
            
            # 将64位数据分组为4个16位，用空格分隔
            def format_64_bits(bits_64):
                return ' '.join([bits_64[i:i+16] for i in range(0, 64, 16)])
            
            # 按要求排列：后64位（低位）在前，前64位（高位）在后
            outfile.write(format_64_bits(low_64_bits) + '\n')   # 先写入低位
            outfile.write(format_64_bits(high_64_bits) + '\n')  # 再写入高位
    
    print(f"处理完成！")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")


# 定义文件路径
input_file = r"./a0_bin copy.txt"
output_file = r"./a0_bin_copy_processed.txt"
output_file_spaced = r"./a0_bin_processed_spaced.txt"

# 执行处理（无空格格式）
print("=== 处理为连续二进制格式 ===")
process_bin_data(input_file, output_file)

print("\n=== 处理为16位分组格式 ===")
# 执行处理（16位分组格式）
process_bin_data_with_spacing(input_file, output_file_spaced)

print(f"\n生成了两个输出文件：")
print(f"1. {output_file} - 连续二进制格式")
print(f"2. {output_file_spaced} - 16位分组格式")
