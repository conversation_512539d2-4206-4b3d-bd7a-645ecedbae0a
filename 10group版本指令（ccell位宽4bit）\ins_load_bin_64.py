import os

class ins_base:
    def __init__(self):
        self.module_id = 0
        self.buffer_id = 0
        self.ins_id    = 0
        self.ins_str_bin   = ''

    def twos_complement_to_int(self,binary_str):
        num_bits = len(binary_str)
        num = int(binary_str, 2)
        if binary_str[0] == '1':
            num -= (1 << num_bits)
        return num

    def parse_bin_ins(self,parse_position):
        parse_field = self.ins_str_bin[:parse_position]
        self.ins_str_bin = self.ins_str_bin[parse_position:]
        return parse_field
    
    def print_dec_ins_info(self,ins_file_ptr_dec):
        self.print_dec()
        ins_file_ptr_dec.write(self.ins_dec+'\n')
        
class config1(ins_base):
    def __init__(self):
        super().__init__()
        self.Stride    = 0
        self.DW        = 0
        self.W0        = 0
        self.H0        = 0
        self.C0        = 0

    def read_bin_ins(self,bin_ins_string):
        self.ins_str_bin = bin_ins_string
        #can not change the order of below lines
        self.module_id   = self.parse_bin_ins( 3)
        self.buffer_id   = self.parse_bin_ins( 4) 
        self.ins_id      = self.parse_bin_ins( 3) 
        self.Stride      = self.parse_bin_ins(13)
        self.DW          = self.parse_bin_ins( 2) 
        self.W0          = self.parse_bin_ins(13)
        self.H0          = self.parse_bin_ins(13)
        self.C0          = self.parse_bin_ins(13)
   
        self.module_id   = int(self.module_id  ,2)
        self.buffer_id   = int(self.buffer_id  ,2)
        self.ins_id      = int(self.ins_id     ,2)
        self.Stride      = int(self.Stride     ,2)
        self.DW          = int(self.DW         ,2)
        self.W0          = int(self.W0         ,2)
        self.H0          = int(self.H0         ,2)
        self.C0          = int(self.C0         ,2)

    def print_dec(self):
        self.ins_dec = ""
        self.ins_dec += f"Stride is {self.Stride}\n"
        self.ins_dec += f"DW is {self.DW}\n"
        self.ins_dec += f"W0 is {self.W0}\n"
        self.ins_dec += f"H0 is {self.H0}\n"
        self.ins_dec += f"C0 is {self.C0}\n"

class config2(ins_base):
    def __init__(self):
        super().__init__()
        self.TW     = 0
        self.Lens   = 0
        self.WBlock = 0
        self.HBlock = 0
        self.CBlock = 0

    def read_bin_ins(self,bin_ins_string):
        self.ins_str_bin = bin_ins_string
        self.module_id  = self.parse_bin_ins( 3)
        self.buffer_id  = self.parse_bin_ins( 4)
        self.ins_id     = self.parse_bin_ins( 3)
        self.TW         = self.parse_bin_ins(13)
        self.Lens       = self.parse_bin_ins( 8)
        self.WBlock     = self.parse_bin_ins(11)
        self.HBlock     = self.parse_bin_ins(11)
        self.CBlock     = self.parse_bin_ins(11)
    
        self.module_id  = int(self.module_id,2)  
        self.buffer_id  = int(self.buffer_id,2)  
        self.ins_id     = int(self.ins_id   ,2)  
        self.TW         = int(self.TW       ,2)
        self.Lens       = int(self.Lens     ,2) + 1
        self.WBlock     = int(self.WBlock   ,2) + 1
        self.HBlock     = int(self.HBlock   ,2) + 1
        self.CBlock     = int(self.CBlock   ,2) + 1
    
    def print_dec(self):
        self.ins_dec = ""
        self.ins_dec += f"TW     is {self.TW}\n"
        self.ins_dec += f"Lens   is {self.Lens  }\n"
        self.ins_dec += f"WBlock is {self.WBlock}\n"
        self.ins_dec += f"HBlock is {self.HBlock}\n"
        self.ins_dec += f"CBlock is {self.CBlock}\n"

class config3(ins_base):
    def __init__(self):
        super().__init__()
        self.WCell        = 0
        self.HCell        = 0
        self.CCell        = 0
        self.access_times = 0
        self.WWindow      = 0
        self.HWindow      = 0
        self.CWindow      = 0

    def read_bin_ins(self,bin_ins_string):
        self.ins_str_bin = bin_ins_string
        self.module_id    = self.parse_bin_ins(3) 
        self.buffer_id    = self.parse_bin_ins(4) 
        self.ins_id       = self.parse_bin_ins(3) 
        self.WCell        = self.parse_bin_ins(1)
        self.HCell        = self.parse_bin_ins(7)
        self.CCell        = self.parse_bin_ins(4)
        self.access_times = self.parse_bin_ins(9)
        self.WWindow      = self.parse_bin_ins(11)
        self.HWindow      = self.parse_bin_ins(11)
        self.CWindow      = self.parse_bin_ins(11)

        self.module_id    = int(self.module_id   ,2)
        self.buffer_id    = int(self.buffer_id   ,2)
        self.ins_id       = int(self.ins_id      ,2)
        self.WCell        = int(self.WCell       ,2) + 1
        self.HCell        = int(self.HCell       ,2) + 1
        self.CCell        = int(self.CCell       ,2) + 1
        self.access_times = int(self.access_times,2) + 1
        self.WWindow      = int(self.WWindow     ,2) + 1
        self.HWindow      = int(self.HWindow     ,2) + 1
        self.CWindow      = int(self.CWindow     ,2) + 1
    
    def print_dec(self):
        self.ins_dec = ""
        self.ins_dec += f"WCell is {self.WCell}\n"
        self.ins_dec += f"HCell is {self.HCell}\n"
        self.ins_dec += f"CCell is {self.CCell}\n"
        self.ins_dec += f"access_times is {self.access_times}\n"
        self.ins_dec += f"WWindow is {self.WWindow}\n"
        self.ins_dec += f"HWindow is {self.HWindow}\n"
        self.ins_dec += f"CWindow is {self.CWindow}\n"

class config4(ins_base):
    def __init__(self):
        super().__init__()
        self.WWindowstart  = 0
        self.WWindowend    = 0
        self.HWindowstart  = 0
        self.HWindowend    = 0
        self.CWindowstart  = 0
        self.CWindowend    = 0
        self.OrderWindow   = 0

    def read_bin_ins(self,bin_ins_string):
        self.ins_str_bin = bin_ins_string
        self.module_id    = self.parse_bin_ins( 3) 
        self.buffer_id    = self.parse_bin_ins( 4) 
        self.ins_id       = self.parse_bin_ins( 3) 
        self.WWindowstart = self.parse_bin_ins( 6)
        self.WWindowend   = self.parse_bin_ins(11)
        self.HWindowstart = self.parse_bin_ins( 6)
        self.HWindowend   = self.parse_bin_ins(11)
        self.CWindowstart = self.parse_bin_ins( 6)
        self.CWindowend   = self.parse_bin_ins(11)
        self.OrderWindow  = self.parse_bin_ins( 3)
        
        self.module_id    = int(self.module_id  ,2)   
        self.buffer_id    = int(self.buffer_id  ,2)   
        self.ins_id       = int(self.ins_id     ,2)   
        self.WWindowend   = int(self.WWindowend ,2)  
        self.HWindowend   = int(self.HWindowend ,2)  
        self.CWindowend   = int(self.CWindowend ,2)  
        self.OrderWindow  = int(self.OrderWindow,2)  
        self.WWindowstart = self.twos_complement_to_int(self.WWindowstart) 
        self.HWindowstart = self.twos_complement_to_int(self.HWindowstart) 
        self.CWindowstart = self.twos_complement_to_int(self.CWindowstart) 
        
    def print_dec(self):
        self.ins_dec = ""
        self.ins_dec += f"WWindowstart is {self.WWindowstart}\n"
        self.ins_dec += f"HWindowstart is {self.HWindowstart}\n"
        self.ins_dec += f"CWindowstart is {self.CWindowstart}\n"
        self.ins_dec += f"OrderWindow is {self.OrderWindow}\n"
        self.ins_dec += f"WWindowend is {self.WWindowend}\n"
        self.ins_dec += f"HWindowend is {self.HWindowend}\n"
        self.ins_dec += f"CWindowend is {self.CWindowend}\n"

class config5(ins_base):
    def __init__(self):
        super().__init__()
        self.data_num = 0
        self.des_port_id = 0
        self.src_port_id = 0
        self.feature_map_initial_addr_high = 0
    
    def read_bin_ins(self,bin_ins_string):
        self.ins_str_bin                   = bin_ins_string
        self.module_id                     = self.parse_bin_ins( 3) 
        self.buffer_id                     = self.parse_bin_ins( 4) 
        self.ins_id                        = self.parse_bin_ins( 3) 
        self.data_num                      = self.parse_bin_ins(32)
        self.des_port_id                   = self.parse_bin_ins( 4)
        self.src_port_id                   = self.parse_bin_ins( 4)
        self.feature_map_initial_addr_high = self.parse_bin_ins( 14)

        self.module_id                     = int(self.module_id  ,2) 
        self.buffer_id                     = int(self.buffer_id  ,2) 
        self.ins_id                        = int(self.ins_id     ,2) 
        self.data_num                      = int(self.data_num   ,2)
        self.des_port_id                   = int(self.des_port_id,2)
        self.src_port_id                   = int(self.src_port_id,2)
        self.feature_map_initial_addr_high = int(self.feature_map_initial_addr_high,2)

    def print_dec(self):
        self.ins_dec = ""
        self.ins_dec += f"data_num    is {self.data_num}\n"
        self.ins_dec += f"des_port_id is {self.des_port_id}\n"
        self.ins_dec += f"src_port_id is {self.src_port_id}\n"
        self.ins_dec += f"feature_map_initial_addr_high is {self.feature_map_initial_addr_high}\n"
        self.ins_dec += f"feature_map_initial_addr_high_bin is {format(self.feature_map_initial_addr_high,'014b')}\n"

class work_ins(ins_base):
    def __init__(self):
        super().__init__()
        self.WWindowstep   = 0 
        self.HWindowstep   = 0 
        self.CWindowstep   = 0 
        self.WCellstep     = 0 
        self.HCellstep     = 0 
        self.CCellstep     = 0 
        self.OrderCell     = 0 
        self.work_mode     = 0 
        self.feature_map_initial_addr_low = 0 
    
    def read_bin_ins(self,bin_ins_string):
        self.ins_str_bin = bin_ins_string
        self.module_id                    = self.parse_bin_ins(3)
        self.buffer_id                    = self.parse_bin_ins(4)
        self.ins_id                       = self.parse_bin_ins(3)
        self.WWindowstep                  = self.parse_bin_ins(6)
        self.HWindowstep                  = self.parse_bin_ins(6)
        self.CWindowstep                  = self.parse_bin_ins(6)
        self.WCellstep                    = self.parse_bin_ins(6)
        self.HCellstep                    = self.parse_bin_ins(6)
        self.CCellstep                    = self.parse_bin_ins(6)
        self.OrderCell                    = self.parse_bin_ins(3)
        self.work_mode                    = self.parse_bin_ins(2)
        self.feature_map_initial_addr_low = self.parse_bin_ins(13)

        self.module_id                    = int(self.module_id                ,2)
        self.buffer_id                    = int(self.buffer_id                ,2)
        self.ins_id                       = int(self.ins_id                   ,2)
        self.WWindowstep                  = int(self.WWindowstep              ,2) + 1
        self.HWindowstep                  = int(self.HWindowstep              ,2) + 1
        self.CWindowstep                  = int(self.CWindowstep              ,2) + 1
        self.WCellstep                    = int(self.WCellstep                ,2) + 1
        self.HCellstep                    = int(self.HCellstep                ,2) + 1
        self.CCellstep                    = int(self.CCellstep                ,2) + 1
        self.OrderCell                    = int(self.OrderCell                ,2)
        self.work_mode                    = int(self.work_mode                ,2)
        self.feature_map_initial_addr_low = int(self.feature_map_initial_addr_low ,2)
    
    def print_dec(self):
        self.ins_dec = ""
        self.ins_dec += f"buffer_id is {self.buffer_id}\n"
        self.ins_dec += f"WWindowstep is {self.WWindowstep}\n"
        self.ins_dec += f"HWindowstep is {self.HWindowstep}\n"
        self.ins_dec += f"CWindowstep is {self.CWindowstep}\n"
        self.ins_dec += f"OrderCell is {self.OrderCell}\n"
        self.ins_dec += f"WCellstep is {self.WCellstep}\n"
        self.ins_dec += f"HCellstep is {self.HCellstep}\n"
        self.ins_dec += f"CCellstep is {self.CCellstep}\n"
        self.ins_dec += f"work_mode is {self.work_mode}\n"
        self.ins_dec += f"feature_map_initial_addr_low is {self.feature_map_initial_addr_low}\n"
        self.ins_dec += f"feature_map_initial_addr_low_bin is {format(self.feature_map_initial_addr_low,'013b')}\n"

def read_file(filename):
    with open(filename, 'r', encoding='utf-8') as file:
        lines = file.readlines()
        result = [line.strip() for line in lines]
        return result

if __name__ == '__main__' :
    def read_buffer_ins_top():
        bin_filename = "./8.5.txt"
        dec_filename = "./8.5_info.txt"

        bin_string_buffer_ins_list = read_file(bin_filename)

        buffer_ins_list = []
        round_idx = 1
        with open(dec_filename, 'a') as dec_fileptr:
            for bin_string_buffer_ins in bin_string_buffer_ins_list:
                object_list = []
                object_list.append(work_ins())
                object_list.append(config1() )
                object_list.append(config2() )
                object_list.append(config3() )
                object_list.append(config4() )
                object_list.append(config5() )

                ins_id_string = bin_string_buffer_ins[7:10]
                ins_id = int(ins_id_string,2)

                ins_object = object_list[ins_id]
                ins_object.read_bin_ins(bin_string_buffer_ins)
                buffer_ins_list.append(ins_object)

                ins_object.print_dec_ins_info(dec_fileptr)
                if ins_id == 0:
                    dec_fileptr.write(f'********************************************round {round_idx}:********************************************'+'\n')
                    round_idx += 1

read_buffer_ins_top()