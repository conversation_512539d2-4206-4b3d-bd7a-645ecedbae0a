import numpy as np
import matplotlib.pyplot as plt # type: ignore
from matplotlib import rcParams # type: ignore

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei']  # 设置字体为 SimHei (黑体)
rcParams['axes.unicode_minus'] = False   # 正常显示负号

# 读取文件并转换为浮点数组
def load_data(file_path):
    try:
        with open(file_path, 'r') as file:
            data = file.readlines()
        return np.array([float(x.strip()) for x in data])  # 转为浮点数组
    except ValueError as e:
        print(f"文件解析错误: {e}")
        return None
    except FileNotFoundError:
        print(f"文件未找到: {file_path}")
        return None

# 文件路径

file1 = r"C:\Users\<USER>\Desktop\自演化\RPCCC\2Capsule\卷积脚本\save_data\post_trans_data\a0_palne.txt"
file2 = r"C:\Users\<USER>\Desktop\自演化\RPCCC\2Capsule\卷积脚本\第一层卷积\chip_result\conv1_WDATA_4096_result.txt"
# 加载两组数据
A = load_data(file1)
B = load_data(file2)

if A is not None and B is not None:
    # 确保数据长度一致
    if len(A) != len(B):
        print("数据长度不一致，请检查输入文件！")
    else:
        # 计算误差
        errors = A - B

        # 计算均方误差
        mse = np.mean(errors ** 2)

        # 计算平均相对误差 (过滤分母为 0 的情况)
        mask = (A != 0) & (B != 0)  # 筛选非零元素
        print(mask)
        # mask = A != 0
        if np.any(mask):  # 确保至少存在一个非零值
            # mape = np.mean(np.abs(errors[mask] / A[mask]))
            mape = np.mean(np.abs(errors[mask] / A[mask])) * 100
        else:
            mape = None

        # 绘制图表
        plt.figure(figsize=(12, 8))

        # 第一组数据图
        plt.subplot(3, 1, 1)
        plt.plot(A, label='PC (组1)', marker='', linestyle='-', color='blue')
        plt.title("组1 PC段输出结果")
        plt.xlabel("点位")
        plt.ylabel("值")
        plt.legend()
        plt.grid(alpha=0.4)

        # 第二组数据图
        plt.subplot(3, 1, 2)
        plt.plot(B, label='片外 (组2)', marker='', linestyle='-', color='orange')
        plt.title("组2 片外归一结果")
        plt.xlabel("点位")
        plt.ylabel("值")
        plt.legend()
        plt.grid(alpha=0.4)

        # 绘制误差条形图
        plt.subplot(3, 1, 3)
        plt.bar(range(len(errors)), errors, label='误差', alpha=0.6, color='red')
        plt.axhline(0, color='black', linestyle='--', linewidth=1)  # 添加零误差线
        error_title = f"误差分布 (MSE: {mse:.6f}"
        if mape is not None:
            # error_title += f"，MAPE: {mape:.2f})"
            error_title += f"，MAPE: {mape:.2f}%)"
        else:
            error_title += ")"
        plt.title(error_title)
        plt.xlabel("索引")
        plt.ylabel("误差值")
        plt.legend()
        plt.grid(alpha=0.4)

        plt.tight_layout()
        plt.show()
else:
    print("数据加载失败，无法进行后续计算。")
