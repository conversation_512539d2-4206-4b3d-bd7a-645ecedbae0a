1*1卷积，卷积核个数128，输出做ReLU激活；输入80*80*128，输出80*80*128

（权重block）1*128*128   1*1*128，128个卷积核 
（输入block）80*80*128   10*40*128 16次任务切换
（输出block）80*80*128   10*40*128 16次任务切换


pe缓存配置(输出缓存、输入缓存、权重缓存)：

输出缓存
[60:57] 4位 start_bank_ID： 向量列的起始bank号
[56:44] 13位 ODB_2_MC_trans_num： ODB1和ODB2的切换次数
[43] 1位 Priority：ODB1优先，配为0
[42:40] 3位 Config_r：遍历从reg0开始的多少个寄存器
[39:36] 4位 Config_b：向量行模式，只能从bank0开始，可以规定用几个bank；
[35] 1位 Config_m：1为向量列输出；0为向量行输出
[34] 1位 Config_sv：1为向量输出；0为标量输出

卷积结果都是标量，全部存满之后向外发数

输入缓存
[33:32] 2位 I_DSP （选择发数模式） 00:将MC发送的数据仅发给IDB1
[31] 1位 RD       （是否为LD指令读取模式） 1：是，可以用LD、ST指令读取
[30] 1位 FC       （是否为全连接读取模式） 1：是
[29:25] 5位 NUM   （全连接读取数据个数）
[24:23] 2位 CONV  （是否为卷积模式） 01：16bit的卷积模式
[22:20] 3位 COUNT （卷积模式下，在一次输入buffer中需要读取的数据个数（给一个pe的））
[19:18] 2位 STEP  （卷积模式下，从输入buffer中取数，的步长） 
[17:10] 8位 W_ROW （8行PE的工作状态）最低位指示第一行PE

权重缓存
[9:0] 10位 W_COL （10列PE哪一列需要下发权重数据）


数据流配置：
0
0



