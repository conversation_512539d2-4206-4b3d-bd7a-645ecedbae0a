
ins_gen_bin-hex_64-128.py说明：
是用来产生二进制、十六进制指令的脚本，通过excel读取数值，输出txt形式的指令
可以输出64bit一行的、128bit一行的二进制指令；以及16bit十六进制指令
1.存储有一些配置是（实际值减一）配置的，在该脚本中已经对这部分进行了处理，因此所有减一配置的，都在excel中填入实际值；
2.填入excel中的所有数，为整数类型；（如order = 001时，配置为1）；addr的值已经做了处理，在addr下直接填入实际值；
3.使用之前先对脚本中读入excel的路径进行修改（99行）、对脚本中读出ins.txt的路径进行修改（107行）
4.excel中一行的配置，会产生五条配置指令、一条工作指令（按顺序产生）
5.产生ins的文件名在excel中的第一行，如果文件名一致，指令一直在这个文件名下追加产生；如果想要读出多组ins文件，修改文件名即可
6.使用时修改104行的路径、106行的excel文件名，运行即可
7.data_num的值不需要在excel里填入，脚本不会读取这一项；脚本读取相关数值计算得到data_num，填入指令。可以用下面的脚本验证；


ins_load_bin_64.py说明：
用来解析生成的指令，用来查错
可以对64bit一行的存储指令进行解析，拆分字段，输出txt形式的指令信息
1.修改绝对路径，运行即可