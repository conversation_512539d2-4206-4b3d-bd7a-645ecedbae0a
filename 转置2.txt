pe数据流，108位，4行64bit
0010000000000000000000000000000100000000000000000000000000000000
0010000000000000000000000000000100000000000000000000000000000000
0010000000000000000000000000000100000000000000000000000000000000
0010000000000000000000000000000100000000000000000001000000000011

00100000000000000000000000000001000000000000000000000000000000000010000000000000000000000000000100000000000000000000000000000000
00100000000000000000000000000001000000000000000000000000000000000010000000000000000000000000000100000000000000000001000000000011

001（PE） + 


pe输入缓存 1*5*1，pe输出缓存 1*5*1

输出缓存29bit + 输入缓存24bit +输入缓存和权重缓存共用8bit + 权重10bit

1000000000000000000001000000110000000000110101000000010000000000
0000000000000000000000000000000000000000000000000000000000000000

起始位为1，代表pe缓存指令




00100000000000000000000000000001000000000000000000000000000000000010000000000000000000000000000100000000000000000000000000000000
00100000000000000000000000000001000000000000000000000000000000000010000000000000000000000000000100000000000000000001000000000011
10000000000000000000010000001100000000001101010000000100000000000000000000000000000000000000000000000000000000000000000000000000



总控 起始地址+指令条数