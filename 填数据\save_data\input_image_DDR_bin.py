def convert_to_bin_and_save(input_file, output_file_bin, output_file_dec):
    # 打开输入和两个输出文件
    with open(input_file, 'r') as infile, open(output_file_bin, 'w') as outfile_bin, open(output_file_dec, 'w') as outfile_dec:
        # 读取文件中的每一行
        numbers = infile.readlines()

        # 存储当前行的二进制数和十进制数
        current_line_bin = []
        current_line_dec = []

        for num in numbers:
            # 移除可能的换行符并转换为浮点数
            num = float(num.strip())

            # 将数值乘以 256 并四舍五入
            num_dec = round(num * 256)

            # 转换为16位二进制补码格式
            if num_dec < 0:
                num_bin = format((1 << 16) + num_dec, '016b')  # 负数的补码
            else:
                num_bin = format(num_dec, '016b')  # 正数的补码

            # 将二进制数和十进制数加入当前行
            current_line_bin.append(num_bin)
            current_line_dec.append(str(num_dec))

            # 如果当前行有8个数，写入两个文件并清空当前行
            if len(current_line_bin) == 8:
                # 将当前行的二进制数从右到左写入二进制文件
                outfile_bin.write(" ".join(current_line_bin[::-1]) + '\n')
                # 将当前行的十进制数从右到左写入十进制文件
                outfile_dec.write(" ".join(current_line_dec[::-1]) + '\n')
                current_line_bin = []
                current_line_dec = []

        # 如果最后还有不足8个数的剩余部分，写入两个文件
        if current_line_bin:
            outfile_bin.write(" ".join(current_line_bin[::-1]) + '\n')
            outfile_dec.write(" ".join(current_line_dec[::-1]) + '\n')


import numpy as np

# 定义文件路径
npy_file_path = r"input_npy\processed_image.npy"  # 替换为你的.npy文件路径

txt_file_hwc = r"input_npy\processed_image.txt"   # 按(H, W, C)展平的输出文件路径

# 使用示例
output_file_bin = r'input_npy\processed_image_16_bin.txt'  # 输出的二进制格式文件路径
output_file_dec = r'input_npy\processed_image_16_dec.txt'  # 


# input_file = r'fz_result\FZ\fz_conv1_WDATA_result.txt'  # 输入的txt文件路径
# output_file_bin = r'fz_input_image\VGG_input_fz_conv1_bin.txt'  # 输出的二进制格式文件路径
# output_file_dec = r'fz_input_image\VGG_input_fz_conv1_10.txt'  # 输出的十进制格式文件路径

# 读取npy文件1
data = np.load(npy_file_path)  # 假设数据形状为 (1, 3, 32, 32)
print(data.shape)

# 检查数据是否为 4 维，且形状为 (1, C, H, W)
if data.ndim != 4 or data.shape[0] != 1:
    raise ValueError(f"输入数据的形状为 {data.shape}，需要是 (1, C, H, W) 格式的 4 维数组。")

# 去掉第一个维度 (1)，得到形状为 (C, H, W)
data = np.squeeze(data, axis=0)  # 去掉第一个维度，得到 (3, 32, 32)
#data = np.round(data * 1).astype(int)
data = (data).astype(float)

# 展平数据为 (H, W, C) 格式
flattened_hwc = data.transpose(1, 2, 0).flatten(order='F')  # 转置为 (H, W, C) 后展平
# 保存数据为 .txt 文件
with open(txt_file_hwc, mode='w', encoding='utf-8') as file:
    file.writelines(f"{val}\n" for val in flattened_hwc)

print(f"数据已按 (H, W, C) 展平并保存到 {txt_file_hwc}")


convert_to_bin_and_save(txt_file_hwc, output_file_bin, output_file_dec)