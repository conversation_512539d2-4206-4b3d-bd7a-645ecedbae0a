def convert_to_bin_and_save(input_file, output_file_bin, output_file_dec):
    """
    将已经按(H, W, C)展平的文本文件转换为每行8个数据、从右到左排列的格式

    参数:
    input_file: 输入的文本文件路径（每行一个数值）
    output_file_bin: 输出的二进制格式文件路径
    output_file_dec: 输出的十进制格式文件路径

    功能:
    - 读取文本文件中的浮点数
    - 将每个数值乘以256并四舍五入转换为整数
    - 转换为16位二进制补码格式
    - 每8个数为一行，从右到左排列输出
    - 同时生成二进制和十进制两种格式的输出文件
    """
    # 打开输入和两个输出文件
    with open(input_file, 'r') as infile, open(output_file_bin, 'w') as outfile_bin, open(output_file_dec, 'w') as outfile_dec:
        # 读取文件中的每一行
        numbers = infile.readlines()

        # 存储当前行的二进制数和十进制数
        current_line_bin = []
        current_line_dec = []

        for num in numbers:
            # 移除可能的换行符并转换为浮点数
            num = float(num.strip())

            # 将数值乘以 256 并四舍五入
            num_dec = round(num * 256)

            # 转换为16位二进制补码格式
            if num_dec < 0:
                num_bin = format((1 << 16) + num_dec, '016b')  # 负数的补码
            else:
                num_bin = format(num_dec, '016b')  # 正数的补码

            # 将二进制数和十进制数加入当前行
            current_line_bin.append(num_bin)
            current_line_dec.append(str(num_dec))

            # 如果当前行有8个数，写入两个文件并清空当前行
            if len(current_line_bin) == 8:
                # 将当前行的二进制数从右到左写入二进制文件
                outfile_bin.write(" ".join(current_line_bin[::-1]) + '\n')
                # 将当前行的十进制数从右到左写入十进制文件
                outfile_dec.write(" ".join(current_line_dec[::-1]) + '\n')
                current_line_bin = []
                current_line_dec = []

        # 如果最后还有不足8个数的剩余部分，写入两个文件
        if current_line_bin:
            outfile_bin.write(" ".join(current_line_bin[::-1]) + '\n')
            outfile_dec.write(" ".join(current_line_dec[::-1]) + '\n')


# 定义文件路径
input_file = r"./a0.txt"   # 已经按(H, W, C)展平的输入文件路径
output_file_bin = r'./a0_bin.txt'  # 输出的二进制格式文件路径
output_file_dec = r'./a0_dec.txt'  # 输出的十进制格式文件路径

# 执行转换
convert_to_bin_and_save(input_file, output_file_bin, output_file_dec)

print(f"转换完成！")
print(f"输入文件: {input_file}")
print(f"二进制输出文件: {output_file_bin}")
print(f"十进制输出文件: {output_file_dec}")