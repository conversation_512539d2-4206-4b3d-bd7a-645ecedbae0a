import os

class HexDataProcessor:
    def __init__(self, fz_file, fz16_file, fz10_file, result_path):
        self.fz_file = fz_file
        self.fz16_file = fz16_file
        self.fz10_file = fz10_file
        self.result_path = result_path

    def read_hex_data(self):
        with open(self.fz_file, "r") as f:
            lines = [line.strip() for line in f]
        return lines

    def reverse_and_split_hex(self, data):
        processed_data = []
        for line in data:
            reversed_numbers = [line[i:i+4] for i in range(len(line) - 4, -1, -4)]
            processed_data.extend(reversed_numbers)
        return processed_data

    def save_to_file(self, filepath, data):
        output_dir = os.path.dirname(filepath)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        with open(filepath, "w") as f:
            for item in data:
                f.write(f"{item}\n")

    @staticmethod
    def hex_to_signed_decimal(hex_str, bits=16):
        unsigned_value = int(hex_str, 16)
        if unsigned_value & (1 << (bits - 1)):
            return unsigned_value - (1 << bits)
        return unsigned_value

    def convert_hex_to_signed_decimal(self):
        with open(self.fz16_file, "r") as f:
            hex_lines = [line.strip() for line in f]
        return [self.hex_to_signed_decimal(line) for line in hex_lines]

    def normalize_and_filter_data(self, data, scale_factor=4096):#和输入放大倍数保持一致
        results = []
        for value in data:
            try:
                val = float(value) / scale_factor
                #results.append(max(val, 0))  # clip to 0
                results.append(val)
            except ValueError:
                print(f"无法转换为浮点数: {value}")
        return results

    def process_all(self):
        try:
            # 第一步：读取并处理十六进制数据
            print("读取原始仿真十六进制数据...")
            hex_data = self.read_hex_data()
            reversed_hex_data = self.reverse_and_split_hex(hex_data)
            self.save_to_file(self.fz16_file, reversed_hex_data)
            print(f"处理后的16进制数据已保存到 {self.fz16_file}")

            # 第二步：转换为有符号10进制数据
            print("转换为有符号十进制数据...")
            signed_decimal_data = self.convert_hex_to_signed_decimal()
            self.save_to_file(self.fz10_file, signed_decimal_data)
            print(f"有符号十进制数据已保存到 {self.fz10_file}")

            # 第三步：归一化处理
            print("移位并保存结果...")
            normalized_data = self.normalize_and_filter_data(signed_decimal_data)
            self.save_to_file(self.result_path, normalized_data)
            print(f"移位结果已保存到 {self.result_path}")

        except Exception as e:
            print(f"发生错误: {e}")


if __name__ == "__main__":
    # 文件路径定义
    fz_file = r"C:\Users\<USER>\Desktop\自演化\RPCCC\2Capsule\卷积脚本\第一层卷积\chip_result\WDATA_input4096.txt"
    fz16_file = r"C:\Users\<USER>\Desktop\自演化\RPCCC\2Capsule\卷积脚本\第一层卷积\chip_result\conv1_WDATA_4096_16.txt"
    fz10_file = r"C:\Users\<USER>\Desktop\自演化\RPCCC\2Capsule\卷积脚本\第一层卷积\chip_result\conv1_WDATA_4096_10.txt"
    result_path = r"C:\Users\<USER>\Desktop\自演化\RPCCC\2Capsule\卷积脚本\第一层卷积\chip_result\conv1_WDATA_4096_result.txt"

    # 创建处理器对象并执行处理
    processor = HexDataProcessor(fz_file, fz16_file, fz10_file, result_path)
    processor.process_all()
