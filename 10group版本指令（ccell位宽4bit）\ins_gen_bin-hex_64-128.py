import pandas as pd
import os

class InsCreate:
    def __init__(self, filename):
        self.df = pd.read_excel(filename)
        self.module_str   = "011"
        self.work_ins_str = "000"
        self.config1_str  = "001"
        self.config2_str  = "010"
        self.config3_str  = "011"
        self.config4_str  = "100"
        self.config5_str  = "101"

    def decTobin(self,n,width):
        binary = bin(n)[2:]
        binary = binary.zfill(width)
        return binary

    def int_to_twos_complement(self,n,bits):
        if n >= 0:
            return bin(n)[2:].zfill(bits)
        flipped = (1 << bits) -1 ^ n
        twos_complement = flipped +1
        return bin(twos_complement)[3:]
    
    def configInsCreate1(self, row):
        buffer_id = self.decTobin(int(self.df.loc[row, 'buffer_id']), 4)
        Stride = self.decTobin(int(self.df.loc[row, 'Stride']), 13)
        DW = self.decTobin(int(self.df.loc[row, 'DW']), 2)
        W0 = self.decTobin(int(self.df.loc[row, 'W0']), 13)
        H0 = self.decTobin(int(self.df.loc[row, 'H0']), 13)
        C0 = self.decTobin(int(self.df.loc[row, 'C0']), 13)
        result = self.module_str + buffer_id + self.config1_str + Stride + DW + W0 + H0 + C0
        return result
    
    def configInsCreate2(self, row):
        buffer_id = self.decTobin(int(self.df.loc[row, 'buffer_id']), 4)
        TW = self.decTobin(int(self.df.loc[row, 'TW']), 13)
        Lens = self.decTobin(int(self.df.loc[row, 'Lens']) - 1, 8)
        WBlock = self.decTobin(int(self.df.loc[row, 'WBlock']) - 1, 11)
        HBlock = self.decTobin(int(self.df.loc[row, 'HBlock']) - 1, 11)
        CBlock = self.decTobin(int(self.df.loc[row, 'CBlock']) - 1, 11)
        result = self.module_str + buffer_id + self.config2_str + TW + Lens + WBlock + HBlock + CBlock
        return result
    
    def configInsCreate3(self, row):
        buffer_id = self.decTobin(int(self.df.loc[row, 'buffer_id']), 4)
        WCell = self.decTobin(int(self.df.loc[row, 'WCell']) - 1, 1)
        HCell = self.decTobin(int(self.df.loc[row, 'HCell']) - 1, 7)
        CCell = self.decTobin(int(self.df.loc[row, 'CCell']) - 1, 4)
        access_times = self.decTobin(int(self.df.loc[row, 'access_times']) - 1, 9)
        WWindow = self.decTobin(int(self.df.loc[row, 'WWindow']) - 1, 11)
        HWindow = self.decTobin(int(self.df.loc[row, 'HWindow']) - 1, 11)
        CWindow = self.decTobin(int(self.df.loc[row, 'CWindow']) - 1, 11)
        result = self.module_str + str(buffer_id) + self.config3_str + str(WCell) + str(HCell) + str(CCell) + str(access_times) + str(WWindow) + str(HWindow) + str(CWindow)
        return result

    def configInsCreate4(self, row):
        buffer_id = self.decTobin(int(self.df.loc[row, 'buffer_id']), 4)
        WWindowstart = self.int_to_twos_complement(int(self.df.loc[row, 'WWindowstart']), 6)
        WWindowend = self.decTobin(int(self.df.loc[row, 'WWindowend']), 11)
        HWindowstart = self.int_to_twos_complement(int(self.df.loc[row, 'HWindowstart']), 6)
        HWindowend = self.decTobin(int(self.df.loc[row, 'HWindowend']), 11)
        CWindowstart = self.int_to_twos_complement(int(self.df.loc[row, 'CWindowstart']), 6)
        CWindowend = self.decTobin(int(self.df.loc[row, 'CWindowend']), 11)
        OrderWindow = self.decTobin(int(self.df.loc[row, 'OrderWindow']), 3)
        result = self.module_str + buffer_id + self.config4_str + WWindowstart + WWindowend + HWindowstart + HWindowend + CWindowstart + CWindowend + OrderWindow
        return result

    def configInsCreate5(self, row):
        buffer_id = self.decTobin(int(self.df.loc[row, 'buffer_id']), 4)

        work_mode    = self.df.loc[row, 'work_mode']
        WWindowstart = self.df.loc[row, 'WWindowstart']
        WWindowend   = self.df.loc[row, 'WWindowend']
        HWindowstart = self.df.loc[row, 'HWindowstart']
        HWindowend   = self.df.loc[row, 'HWindowend']
        CWindowstart = self.df.loc[row, 'CWindowstart']
        CWindowend   = self.df.loc[row, 'CWindowend']
        WWindowstep  = self.df.loc[row, 'WWindowstep']
        HWindowstep  = self.df.loc[row, 'HWindowstep']
        CWindowstep  = self.df.loc[row, 'CWindowstep']
        WWindow      = self.df.loc[row, 'WWindow']
        HWindow      = self.df.loc[row, 'HWindow']
        CWindow      = self.df.loc[row, 'CWindow']
        WCell        = self.df.loc[row, 'WCell']
        HCell        = self.df.loc[row, 'HCell']
        CCell        = self.df.loc[row, 'CCell']
        WCellstep    = self.df.loc[row, 'WCellstep']
        HCellstep    = self.df.loc[row, 'HCellstep']
        CCellstep    = self.df.loc[row, 'CCellstep']
        access_times = self.df.loc[row, 'access_times']

        if work_mode == 0 or work_mode == 2 :
            data_num = self.decTobin(0,32)
        else:
            Window_count = 0
            Cell_count = 0
            print(f"work_mode   {work_mode   }")
            print(f"WWindowstart{WWindowstart}")
            print(f"WWindowend  {WWindowend  }")
            print(f"HWindowstart{HWindowstart}")
            print(f"HWindowend  {HWindowend  }")
            print(f"CWindowstart{CWindowstart}")
            print(f"CWindowend  {CWindowend  }")
            print(f"WWindowstep {WWindowstep }")
            print(f"HWindowstep {HWindowstep }")
            print(f"CWindowstep {CWindowstep }")
            print(f"WWindow     {WWindow     }")
            print(f"HWindow     {HWindow     }")
            print(f"CWindow     {CWindow     }")
            print(f"WCell       {WCell       }")
            print(f"HCell       {HCell       }")
            print(f"CCell       {CCell       }")
            print(f"WCellstep   {WCellstep   }")
            print(f"HCellstep   {HCellstep   }")
            print(f"CCellstep   {CCellstep   }")
            print(f"access_times{access_times}")
            for w_idx in range(WWindowstart,WWindowend+1,WWindowstep):
                for h_idx in range(HWindowstart,HWindowend+1,HWindowstep):
                    for w_idx in range(CWindowstart,CWindowend+1,CWindowstep):
                        Window_count += 1
            for w_idx in range(0,WWindow-WCell+1,WCellstep):
                for h_idx in range(0,HWindow-HCell+1,HCellstep):
                    for w_idx in range(0,CWindow-CCell+1,CCellstep):
                        Cell_count += 1
            data_num = self.decTobin(int(Window_count * access_times * Cell_count),32)

         # data_num = self.decTobin(int(self.df.loc[row, 'data_num']),32)
        print(f"data_num is {int(data_num,2)}")

        des_port_id = self.decTobin(int(self.df.loc[row, 'des_port_id']),4)
        src_port_id = self.decTobin(int(self.df.loc[row, 'src_port_id']),4)

        addr = self.decTobin(int(self.df.loc[row, 'addr']), 27)
        # print(f"addr is {addr}")
        addr_high = addr[:14] #高14
        # print(f"addr_high is {addr_high}")

        result = self.module_str + buffer_id + self.config5_str + str(data_num) + str(des_port_id) + str(src_port_id) + str(addr_high)
        return result
        
    def workInsCreate(self, row):
        buffer_id = self.decTobin(int(self.df.loc[row, 'buffer_id']), 4)
        WWindowstep = self.decTobin(int(self.df.loc[row, 'WWindowstep']) - 1 , 6)
        HWindowstep = self.decTobin(int(self.df.loc[row, 'HWindowstep']) - 1 , 6)
        CWindowstep = self.decTobin(int(self.df.loc[row, 'CWindowstep']) - 1 , 6)
        WCellstep = self.decTobin(int(self.df.loc[row, 'WCellstep']) - 1, 6)
        HCellstep = self.decTobin(int(self.df.loc[row, 'HCellstep']) - 1, 6)
        CCellstep = self.decTobin(int(self.df.loc[row, 'CCellstep']) - 1, 6)
        OrderCell = self.decTobin(int(self.df.loc[row, 'OrderCell']), 3)
        work_mode = self.decTobin(int(self.df.loc[row, 'work_mode']), 2)

        addr = self.decTobin(int(self.df.loc[row, 'addr']), 27)
        # print(f"addr is {addr}")
        addr_low = addr[-13:] #低13
        # print(f"addr_low is {addr_low}")
        
        result = str(self.module_str) + buffer_id + self.work_ins_str + WWindowstep + HWindowstep + CWindowstep + WCellstep + HCellstep + CCellstep + OrderCell + work_mode + str(addr_low)
        return result
    
# 将指令写入文件
    def write_to_file(self, row, filename):
        with open(filename, 'a') as f:  
            f.write(self.configInsCreate1(row) + "\n")
            f.write(self.configInsCreate2(row) + "\n")
            f.write(self.configInsCreate3(row) + "\n")
            f.write(self.configInsCreate4(row) + "\n")
            f.write(self.configInsCreate5(row) + "\n")
            f.write(self.workInsCreate(row) + "\n")
if __name__ == '__main__' :
    dir_addr = "./" # 当前文件夹路径

    excel_addr = os.path.join(dir_addr, '自演化2.xlsx')  #excel文件名
    df = pd.read_excel(excel_addr,dtype=str)
    ins_create = InsCreate(excel_addr)

    for row in range(0, len(df), 1):
        filename = os.path.join(dir_addr, f"{df.iloc[row, 0]}_bin_64.txt")
        ins_create.write_to_file(row, filename)

        df.to_excel(excel_addr, index=False)

    def merge_lines_in_file(input_file, output_file, lines_per_group=2):
            with open(input_file, 'r') as infile, open(output_file, 'w') as outfile:
                lines = infile.readlines()
                for i in range(0, len(lines), lines_per_group):

                    group_lines = lines[i:i + lines_per_group]

                    merged_line = ''.join(line.strip() for line in group_lines)

                    outfile.write(merged_line + '\n')

    input_filename_bin_64   = os.path.join(dir_addr, f"{df.iloc[row, 0]}_bin_64.txt")
    output_filename_bin_128 = os.path.join(dir_addr, f"{df.iloc[row, 0]}_bin_128.txt")
    merge_lines_in_file(input_filename_bin_64, output_filename_bin_128)

    def bin_to_hex(binary_str):
            hex_str = hex(int(binary_str, 2))[2:]
            return hex_str.zfill(len(binary_str) // 4)

    def convert_binary_file_to_hex(input_filename,output_file_path):
        with open(input_filename, 'r') as file:
            binary_content = file.read().strip().splitlines()

        hex_content = [bin_to_hex(line) for line in binary_content]

        with open(output_file_path, 'w') as output_file:
            for line in hex_content:
                output_file.write(line + '\n')

    df = pd.read_excel(excel_addr,dtype=str)

    input_filename_bin_64  = os.path.join(dir_addr, f"{df.iloc[row, 0]}_bin_64.txt")  
    output_filename_hex_64 = os.path.join(dir_addr, f"{df.iloc[row, 0]}_hex_64.txt")
    convert_binary_file_to_hex(input_filename_bin_64,output_filename_hex_64)