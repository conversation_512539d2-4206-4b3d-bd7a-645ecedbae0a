# 对于读buffer来说，不要求全部把block块读完，因此windowend值有多种选择
# 读block的范围其实是windowstart、windowend划定的window移动的范围
# window在block里滑动，最后一个window的左上角点坐标是windowend

def generate_window_end_list(windowstart,Block,windowstep):
    windowend_list = []
    end_value = windowstart

    while end_value < Block:
        windowend_list.append(end_value)
        end_value += windowstep
        
    print(f"possible windowend:{windowend_list}")
# 注意windowend必须为正数！

generate_window_end_list(-1,64,16)
# 使用方法，在上面括号里依次填入windowstart,Block,windowstep的值即可
