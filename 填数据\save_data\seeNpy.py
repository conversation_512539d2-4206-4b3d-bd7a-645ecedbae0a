# --coding:utf-8--
import numpy as np

# 定义文件路径
npy_file_path = r"./a1.npy"  # 替换为你的.npy文件路径
txt_file_hwc = r"./a1.txt"   # 按(H, W, C)展平的输出文件路径

# 读取npy文件
data = np.load(npy_file_path)  # 假设数据形状为 (20, C, H, W)
print(f"原始数据形状: {data.shape}")

# 检查数据是否为 4 维，且形状为 (20, C, H, W)
if data.ndim != 4 or data.shape[0] != 20:
    raise ValueError(f"输入数据的形状为 {data.shape}，需要是 (20, C, H, W) 格式的 4 维数组。")

# 转换数据类型为浮点数
data = data.astype(float)

# 存储所有展平后的数据
all_flattened_data = []

# 遍历20个数据块
for i in range(20):
    # 获取第i个数据块，形状为 (C, H, W)
    data_block = data[i]  # 形状: (C, H, W)
    
    # 展平数据为 (H, W, C) 格式
    flattened_hwc = data_block.transpose(1, 2, 0).flatten(order='F')  # 转置为 (H, W, C) 后展平
    
    # 添加到总列表中
    all_flattened_data.extend(flattened_hwc)
    
    print(f"第 {i+1} 个数据块处理完成，形状: {data_block.shape} -> 展平后长度: {len(flattened_hwc)}")

# 保存所有数据为 .txt 文件
with open(txt_file_hwc, mode='w', encoding='utf-8') as file:
    for val in all_flattened_data:
        file.write(f"{val}\n")

print(f"总共处理了 20 个数据块")
print(f"总数据点数量: {len(all_flattened_data)}")
print(f"数据已按 (H, W, C) 展平并顺序保存到 {txt_file_hwc}")

